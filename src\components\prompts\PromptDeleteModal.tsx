/**
 * 提示词删除确认模态窗口组件
 */
import React, { useState } from 'react';
import { Modal } from '@/components/common/modals';

// 用户提示词接口
interface UserPrompt {
  id: string;
  title: string;
  description?: string;
  type: string;
  content?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PromptDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  prompt: UserPrompt | null;
}

export default function PromptDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  prompt
}: PromptDeleteModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
    } finally {
      setIsDeleting(false);
    }
  };

  if (!prompt) return null;

  // 底部按钮组件
  const footerButtons = (
    <div className="flex justify-end space-x-4">
      <button
        type="button"
        onClick={onClose}
        className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-all duration-300 font-medium hover:bg-gray-100/50 rounded-xl"
        disabled={isDeleting}
      >
        取消
      </button>
      <button
        type="button"
        onClick={handleConfirm}
        disabled={isDeleting}
        className={`relative px-8 py-3 rounded-xl font-medium transition-all duration-300 transform flex items-center ${
          isDeleting
            ? 'bg-gray-400 text-white cursor-not-allowed opacity-70'
            : 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 hover:scale-105 hover:shadow-lg hover:shadow-red-500/30 active:scale-95'
        }`}
      >
        {!isDeleting && (
          <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
        )}
        <span className="relative z-10 flex items-center">
          {isDeleting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white mr-2"></div>
              删除中...
            </>
          ) : (
            <>
              <span className="material-icons text-sm mr-2">delete_forever</span>
              确认删除
            </>
          )}
        </span>
      </button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="删除提示词"
      maxWidth="max-w-lg"
      footer={footerButtons}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-red-50/30 to-orange-50/30 rounded-xl"></div>
      <div className="absolute inset-0 opacity-10" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23rgba(239,68,68,0.1)' fill-opacity='1'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      <div className="relative space-y-6">
        {/* 警告图标和消息 */}
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className="w-16 h-16 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
              <span className="material-icons text-white text-2xl">warning</span>
            </div>
          </div>
          <div className="flex-1 pt-2">
            <h3 className="text-xl font-bold text-gray-800 mb-3 flex items-center">
              <span className="material-icons text-red-500 mr-2">delete</span>
              确认删除提示词
            </h3>
            <p className="text-gray-600 mb-4 leading-relaxed">
              您确定要删除提示词 <span className="font-semibold text-gray-800 bg-yellow-100 px-2 py-1 rounded">"{prompt.title}"</span> 吗？
            </p>
            <div className="relative p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200/60 rounded-xl shadow-sm">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center mr-3 mt-0.5 shadow-sm">
                  <span className="material-icons text-white text-sm">info</span>
                </div>
                <div>
                  <p className="text-red-700 font-medium mb-1">重要提醒</p>
                  <p className="text-red-600 text-sm leading-relaxed">
                    此操作无法撤销，删除后将永久丢失该提示词的所有内容。
                  </p>
                </div>
              </div>
              <div className="absolute top-2 right-2 w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

    </Modal>
  );
}
