'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

import TopBar from '@/components/TopBar';
import { PromptGroup } from '@/types/ui';
import PromptFormModal from '@/components/prompts/PromptFormModal';
import PromptDeleteModal from '@/components/prompts/PromptDeleteModal';
import { getCurrentUser } from '@/services/userService';

// 提示词类型映射
const promptTypeMap = {
  'ai_writing': { label: 'AI写作', color: 'bg-[#5a9d6b] text-white', icon: 'create', group: 'novel', gradient: 'from-[#5a9d6b] to-[#4a8d5b]' },
  'ai_polishing': { label: 'AI润色', color: 'bg-[#7D85CC] text-white', icon: 'auto_fix_high', group: 'novel', gradient: 'from-[#7D85CC] to-[#6F9CE0]' }
};

// 提示词分组定义
const promptGroups: PromptGroup[] = [
  {
    label: '小说创作',
    color: 'bg-[#5a9d6b] text-white',
    icon: 'auto_stories',
    types: ['ai_writing', 'ai_polishing']
  }
];

export default function PromptsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [activeGroup, setActiveGroup] = useState(0);
  const [activeType, setActiveType] = useState('');
  const [userPrompts, setUserPrompts] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentUser, setCurrentUser] = useState<any>(null);

  // 模态窗口状态
  const [showFormModal, setShowFormModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingPrompt, setEditingPrompt] = useState<any>(null);
  const [deletingPrompt, setDeletingPrompt] = useState<any>(null);

  // 初始化默认选中的类型和用户信息
  useEffect(() => {
    if (promptGroups.length > 0 && promptGroups[activeGroup].types.length > 0) {
      const defaultType = promptGroups[activeGroup].types[0];
      setActiveType(defaultType);
    }

    // 获取当前用户信息
    const loadCurrentUser = async () => {
      try {
        const user = await getCurrentUser();
        setCurrentUser(user);
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    };

    loadCurrentUser();
  }, [activeGroup]);

  // 加载用户提示词数据
  const loadUserPrompts = async (type: string) => {
    try {
      const response = await fetch('/api/prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category: 'userprompt',
          type: type,
          limit: 50
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUserPrompts(data.data || []);
        }
      }
    } catch (error) {
      console.error('加载用户提示词失败:', error);
      setUserPrompts([]);
    }
  };

  // 加载提示词数据
  const loadPrompts = async (type: string) => {
    try {
      setIsLoading(true);
      await loadUserPrompts(type);
    } catch (error) {
      console.error('加载提示词失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 当选中类型改变时加载提示词
  useEffect(() => {
    if (activeType) {
      loadPrompts(activeType);
    }
  }, [activeType]);

  // 管理功能处理函数
  const handleCreatePrompt = () => {
    setEditingPrompt(null);
    setShowFormModal(true);
  };

  const handleEditPrompt = (prompt: any) => {
    setEditingPrompt(prompt);
    setShowFormModal(true);
  };

  const handleDeletePrompt = (prompt: any) => {
    setDeletingPrompt(prompt);
    setShowDeleteModal(true);
  };

  const handleSavePrompt = async (savedPrompt: any) => {
    // 重新加载提示词列表
    if (activeType) {
      await loadPrompts(activeType);
    }
  };

  const handleConfirmDelete = async () => {
    if (!deletingPrompt) return;

    try {
      const response = await fetch(`/api/prompt?id=${deletingPrompt.id}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '删除失败');
      }

      if (data.success) {
        // 重新加载提示词列表
        if (activeType) {
          await loadPrompts(activeType);
        }
        setShowDeleteModal(false);
        setDeletingPrompt(null);
      } else {
        throw new Error(data.error || '删除失败');
      }
    } catch (error) {
      console.error('删除提示词失败:', error);
      alert(error instanceof Error ? error.message : '删除失败，请稍后再试');
    }
  };

  // 检查是否为当前用户创建的提示词
  const isOwnPrompt = (prompt: any) => {
    return currentUser && prompt.created_by === currentUser.email;
  };

  // 处理大分类切换
  const handleGroupChange = (groupIndex: number) => {
    setActiveGroup(groupIndex);
    const newType = promptGroups[groupIndex].types[0];
    setActiveType(newType);
  };

  // 处理具体类型切换
  const handleTypeChange = (type: string) => {
    setActiveType(type);
  };

  // 过滤提示词
  const filteredPrompts = userPrompts.filter(prompt => {
    const matchesSearch =
      prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (prompt.content && prompt.content.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (prompt.description && prompt.description.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSearch;
  });



  return (
    <div className="flex h-screen bg-bg-color animate-fadeIn overflow-hidden">
      {/* 背景网格 */}
      <div className="grid-background"></div>

      {/* 装饰元素，在小屏幕上减少数量 */}
      <div className="dot hidden md:block" style={{ top: "120px", left: "15%" }}></div>
      <div className="dot" style={{ bottom: "80px", right: "20%" }}></div>
      <div className="dot hidden md:block" style={{ top: "30%", right: "25%" }}></div>
      <div className="dot hidden md:block" style={{ bottom: "40%", left: "30%" }}></div>

      <svg className="wave hidden md:block" style={{ bottom: "20px", left: "10%" }} width="100" height="20" viewBox="0 0 100 20">
        <path d="M0,10 Q25,0 50,10 T100,10" fill="none" stroke="var(--accent-brown)" strokeWidth="2" />
      </svg>

      <div className="flex-1 flex flex-col overflow-hidden">
        <TopBar
          showBackButton={true}
        />
        {/* 横向分类标签导航 */}
        <div className="border-b border-[rgba(120,180,140,0.2)] bg-card-color">
          <div className="w-full px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              {/* 左边：大分类选择器 */}
              <div className="flex space-x-0">
                {promptGroups.map((group, index) => (
                  <button
                    key={group.label}
                    className={`relative px-6 py-4 text-sm font-medium transition-all duration-200 border-b-2 ${
                      activeGroup === index
                        ? 'border-[#00C250] text-[#00C250] bg-[rgba(0,194,80,0.05)]'
                        : 'border-transparent text-text-medium hover:text-text-dark hover:border-[rgba(120,180,140,0.3)]'
                    }`}
                    onClick={() => handleGroupChange(index)}
                  >
                    <div className="flex items-center">
                      <span className={`material-icons text-lg mr-2 ${
                        activeGroup === index ? 'text-[#00C250]' : 'text-text-medium'
                      }`}>
                        {group.icon}
                      </span>
                      {group.label}
                    </div>
                  </button>
                ))}
              </div>

              {/* 竖线分隔符 */}
              <div className="h-8 w-px bg-[rgba(120,180,140,0.3)] mx-4"></div>

              {/* 右边：当前大分类下的具体类型选项 */}
              <div className="flex space-x-0 flex-1">
                {promptGroups[activeGroup].types.map(type => (
                  <button
                    key={type}
                    className={`relative px-4 py-4 text-sm font-medium transition-all duration-200 border-b-2 ${
                      activeType === type
                        ? 'border-[#00C250] text-[#00C250] bg-[rgba(0,194,80,0.05)]'
                        : 'border-transparent text-text-medium hover:text-text-dark hover:border-[rgba(120,180,140,0.3)]'
                    }`}
                    onClick={() => handleTypeChange(type)}
                  >
                    <div className="flex items-center">
                      <span className={`material-icons text-base mr-1 ${
                        activeType === type ? 'text-[#00C250]' : 'text-text-medium'
                      }`}>
                        {promptTypeMap[type as keyof typeof promptTypeMap]?.icon}
                      </span>
                      {promptTypeMap[type as keyof typeof promptTypeMap]?.label}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <main className="flex-1 overflow-auto p-4 sm:p-6 lg:p-8">
          <div className="w-full">
            {/* 工具栏：搜索框和创建按钮 */}
            <div className="relative mb-6 flex flex-wrap items-center gap-4 justify-between">
              {/* 搜索框 */}
              <div className="flex-shrink-0 w-64">
                <div className="relative w-full">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="material-icons text-text-light">search</span>
                  </div>
                  <input
                    type="text"
                    className="block w-full pl-10 pr-3 py-2 border border-[rgba(120,180,140,0.3)] rounded-xl bg-card-color focus:outline-none focus:ring-2 focus:ring-[rgba(120,180,140,0.5)] shadow-sm text-text-dark"
                    placeholder="搜索提示词..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* 创建按钮 */}
              {currentUser && (
                <button
                  onClick={handleCreatePrompt}
                  className="flex items-center px-4 py-2 bg-[#00C250] text-white rounded-lg hover:bg-[#00A844] transition-colors"
                >
                  <span className="material-icons text-sm mr-2">add</span>
                  创建提示词
                </button>
              )}
            </div>

            {/* 提示词卡片网格 */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
              {filteredPrompts.length > 0 ? (
                filteredPrompts.map((prompt, index) => {
                  const typeConfig = promptTypeMap[activeType as keyof typeof promptTypeMap];
                  if (!typeConfig) return null;

                  const colorText = typeConfig.color.split(' ')[1];
                  const bgColor = typeConfig.color.split(' ')[0];

                  return (
                    <div
                      key={prompt.id}
                      className="group relative cursor-pointer animate-fadeIn transform transition-all duration-500 hover:scale-105"
                      style={{
                        animationDelay: `${index * 100}ms`,
                        width: '100%',
                        maxWidth: '340px',
                        height: '280px'
                      }}
                      onClick={() => {/* TODO: 打开详情模态窗口 */}}
                    >
                      {/* 卡片主体 */}
                      <div className="relative h-full bg-gradient-to-br from-white via-white to-gray-50/30 rounded-2xl border border-[rgba(120,180,140,0.2)] shadow-lg hover:shadow-2xl hover:shadow-[rgba(90,157,107,0.15)] transition-all duration-500 overflow-hidden backdrop-blur-sm">
                        {/* 背景装饰 */}
                        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-[rgba(120,180,140,0.03)] opacity-60"></div>
                        <div className="absolute inset-0 opacity-20" style={{
                          backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23rgba(120,180,140,0.08)' fill-opacity='1'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3C/g%3E%3C/svg%3E")`
                        }}></div>

                        {/* 胶带装饰 */}
                        <div className="absolute -top-2 left-6 w-16 h-6 bg-gradient-to-r from-yellow-300/70 to-yellow-400/70 rounded-sm transform -rotate-3 shadow-md z-10"></div>

                        {/* 类型标识条 */}
                        <div className={`absolute top-0 right-0 w-20 h-1 ${bgColor} opacity-60`}></div>
                        <div className="relative flex flex-col h-full p-6 z-10">
                          {/* 顶部区域 */}
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center flex-1 min-w-0">
                              <div className={`w-14 h-14 rounded-2xl ${bgColor} flex items-center justify-center mr-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                                <span className={`material-icons text-2xl ${colorText} group-hover:rotate-12 transition-transform duration-300`}>{typeConfig.icon}</span>
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className="font-bold text-gray-800 text-lg font-ma-shan truncate mb-1 group-hover:text-[#5a9d6b] transition-colors duration-300">
                                  {prompt.title}
                                </h3>
                              </div>
                            </div>

                            {/* 管理按钮 - 只对自己创建的提示词显示 */}
                            {isOwnPrompt(prompt) && (
                              <div className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 flex space-x-2">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditPrompt(prompt);
                                  }}
                                  className="w-8 h-8 rounded-full bg-blue-500/10 hover:bg-blue-500/20 text-blue-600 hover:text-blue-700 transition-all duration-300 flex items-center justify-center hover:scale-110 shadow-sm"
                                  title="编辑"
                                >
                                  <span className="material-icons text-sm">edit</span>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeletePrompt(prompt);
                                  }}
                                  className="w-8 h-8 rounded-full bg-red-500/10 hover:bg-red-500/20 text-red-600 hover:text-red-700 transition-all duration-300 flex items-center justify-center hover:scale-110 shadow-sm"
                                  title="删除"
                                >
                                  <span className="material-icons text-sm">delete</span>
                                </button>
                              </div>
                            )}
                          </div>

                          {/* 描述区域 */}
                          <div className="flex-1 mb-4">
                            <p className="text-gray-600 text-sm leading-relaxed line-clamp-3 group-hover:text-gray-700 transition-colors duration-300">
                              {prompt.description || '这个提示词还没有添加描述...'}
                            </p>
                          </div>

                          {/* 底部信息栏 */}
                          <div className="mt-auto">
                            <div className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50/80 to-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 group-hover:border-[rgba(90,157,107,0.3)] transition-all duration-300">
                              <div className="flex items-center text-xs text-gray-500 group-hover:text-gray-600 transition-colors duration-300">
                                <div className="w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mr-2 shadow-sm">
                                  <span className="material-icons text-white text-xs">schedule</span>
                                </div>
                                <span className="font-medium">{new Date(prompt.updated_at).toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center text-xs text-gray-500 group-hover:text-gray-600 transition-colors duration-300">
                                <span className="font-medium mr-2">{prompt.author_display_id || '未知用户'}</span>
                                <div className="w-6 h-6 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center shadow-sm">
                                  <span className="material-icons text-white text-xs">person</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* 翻页效果 */}
                        <div className="absolute bottom-0 right-0 w-8 h-8 bg-gradient-to-tl from-gray-200/50 to-transparent rounded-tl-2xl transform rotate-0 group-hover:rotate-12 transition-transform duration-300"></div>

                        {/* 悬停发光效果 */}
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-[rgba(90,157,107,0.1)] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      </div>
                    </div>
                  );
                })
              ) : (
                // 无提示词提示 - 美化版
                <div className="col-span-full flex justify-center">
                  <div className="relative max-w-md w-full">
                    <div className="bg-gradient-to-br from-white via-white to-gray-50/30 rounded-3xl border border-[rgba(120,180,140,0.2)] shadow-xl p-12 flex flex-col items-center justify-center text-center backdrop-blur-sm">
                      {/* 背景装饰 */}
                      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-[rgba(120,180,140,0.03)] rounded-3xl"></div>
                      <div className="absolute inset-0 opacity-10 rounded-3xl" style={{
                        backgroundImage: `url("data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23rgba(120,180,140,0.1)' fill-opacity='1'%3E%3Ccircle cx='5' cy='5' r='1.5'/%3E%3C/g%3E%3C/svg%3E")`
                      }}></div>

                      <div className="relative z-10">
                        <div className="w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-6 shadow-inner animate-pulse">
                          <span className="material-icons text-6xl text-gray-400">
                            {searchTerm ? 'search_off' : 'lightbulb_outline'}
                          </span>
                        </div>

                        <h3 className="text-2xl font-bold text-gray-800 mb-3 font-ma-shan">
                          {searchTerm ? '未找到匹配结果' : '暂无提示词'}
                        </h3>

                        <p className="text-gray-600 leading-relaxed mb-8 max-w-sm">
                          {searchTerm
                            ? `没有找到包含 "${searchTerm}" 的提示词，试试其他关键词吧`
                            : `当前 ${promptTypeMap[activeType as keyof typeof promptTypeMap]?.label || '类型'} 下还没有提示词，快来创建第一个吧！`}
                        </p>

                        {searchTerm ? (
                          <button
                            onClick={() => setSearchTerm('')}
                            className="relative px-6 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-2xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-gray-500/30 active:scale-95 flex items-center"
                          >
                            <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                            <span className="relative z-10 flex items-center">
                              <span className="material-icons mr-2">clear</span>
                              清除搜索
                            </span>
                          </button>
                        ) : currentUser && (
                          <button
                            onClick={handleCreatePrompt}
                            className="relative px-8 py-4 bg-gradient-to-r from-[#00C250] to-[#00A844] text-white rounded-2xl font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-green-500/30 active:scale-95 flex items-center"
                          >
                            <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                            <span className="relative z-10 flex items-center">
                              <span className="material-icons mr-2">add_circle</span>
                              创建第一个提示词
                            </span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>


      </div>

      {/* 提示词表单模态窗口 */}
      <PromptFormModal
        isOpen={showFormModal}
        onClose={() => {
          setShowFormModal(false);
          setEditingPrompt(null);
        }}
        onSave={handleSavePrompt}
        prompt={editingPrompt}
        promptType={activeType}
      />

      {/* 删除确认模态窗口 */}
      <PromptDeleteModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setDeletingPrompt(null);
        }}
        onConfirm={handleConfirmDelete}
        prompt={deletingPrompt}
      />
    </div>
  );
}
